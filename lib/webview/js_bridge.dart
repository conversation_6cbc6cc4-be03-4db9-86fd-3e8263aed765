import 'dart:convert';
import 'package:dlyz_flutter/manager/channel_manager.dart';
import 'package:dlyz_flutter/pay/pay_manager.dart';
import 'package:dlyz_flutter/utils/log_util.dart';
import 'package:dlyz_flutter/webview/web_router.dart';
import 'package:dlyz_flutter/webview/webview_wrapper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:provider/provider.dart';
import '../pages/bind/character_bind_page.dart';
import '../providers/user_provider.dart';
import '../utils/permission_utils.dart';
import '../components/game_character_binding_dialog.dart';

/// JavaScript消息结构
class JSMessage {
  final String method;
  final Map<String, dynamic> data;

  JSMessage({
    required this.method,
    required this.data,
  });

  factory JSMessage.fromJson(Map<String, dynamic> json) {
    return JSMessage(
      method: json['method'] ?? '',
      data: json['data'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'data': data,
    };
  }
}

/// JavaScript通信桥接器
class JSBridge {
  /// 消息处理器映射
  final Map<String, Function(JSMessage)> _handlers = {};
  
  /// 通用消息监听器
  Function(JSMessage)? onMessage;

  final BuildContext _context;

  WebViewWrapperController? _webViewController;

  JSBridge(this._context, {WebViewWrapperController? webViewController})
      : _webViewController = webViewController {
    _registerDefaultHandlers();
  }

  /// 注册默认处理器
  void _registerDefaultHandlers() {
    // 跳转外部浏览器
    registerHandler('openActionBrowser', (message) {
      String url = message.data["url"] ?? '';
      if (url.isNotEmpty) {
        ChannelManager().openUrl(url: url);
      }
    });

    // Toast提示
    registerHandler('showToast', (message) {
      Fluttertoast.showToast(
        msg: message.data["message"] ?? '',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
      );
    });

    // 关闭页面
    registerHandler('close', (message) {
      Navigator.pop(_context);
    });

    //查询权限是否授权
    registerHandler('isHasPermission', (message) async {
      String permission = message.data["permissionName"] ?? '';
      PermissionType? permissionType;
      if ('notification' == permission) {
        permissionType = PermissionType.notification;
      }
      if (permissionType == null) return;
      final isHasPermission = await PermissionUtils.isPermissionGranted(permissionType);
      String callback = message.data["callback"] ?? '';
      LogUtil.d("isHasPermission: $isHasPermission, callback = $callback");
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, {
          'isHasPermission': isHasPermission
        });
      }
    });

    //申请权限
    registerHandler('requestPermission', (message) async {
      String permission = message.data["permissionName"] ?? '';
      PermissionType? permissionType;
      if ('notification' == permission) {
        permissionType = PermissionType.notification;
      }
      if (permissionType == null) return;
      PermissionUtils.requestPermission(_context, permissionType).then((isGranted) {
        String callback = message.data["callback"] ?? '';
        LogUtil.d("requestPermission: $isGranted, callback = $callback");
        if (callback.isNotEmpty) {
          _executeJsMethod(callback, {
            'isGranted': isGranted
          });
        }
      });
    });

    //拉起角色授权弹窗
    registerHandler('showGameBindingDialog', (message) async {
      String callback = message.data["callback"] ?? '';
      // TODO: 待完善绑定弹窗
      GameCharacterBindingDialog.show(
        context: _context,
        onCharacterSelected: (character) {
          LogUtil.d("选择了角色: ${character.name}");
          // 可以在这里处理角色选择逻辑
          if (callback.isNotEmpty) {
            _executeJsMethod(callback, {});
          }
        },
        onBindOtherCharacter: () {
          LogUtil.d("点击了绑定其他角色");
          // 可以在这里跳转到角色绑定页面
          if (callback.isNotEmpty) {
            _executeJsMethod(callback, {});
          }
        },
        onClose: () {
          LogUtil.d("关闭了角色绑定弹窗");
          if (callback.isNotEmpty) {
            _executeJsMethod(callback, {});
          }
        },
      );
    });

    //获取绑定角色列表
    registerHandler('getBindingRoleData', (message) async {
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, {
          // TODO: 这里后续会替换成接口获取
          'data': []
        });
      }
    });

    //跳转游戏绑定页
    registerHandler('jumpGameBindingPage', (message) async {
      //todo 待替换测试数据
      var result = await Navigator.push(
        _context,
        MaterialPageRoute(
          builder: (context) => const CharacterBindPage(
            phoneNumber: '138****8888', // 替换为实际的手机号
          ),
        ),
      );
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, {});
      }
    });

    //打开webview
    registerHandler('openWebview', (message) async {
      String url = message.data["url"] ?? '';
      final ticket = _context.read<UserProvider>().currentTicket;
      Map<String, dynamic> params = {
        'app_ticket': ticket
      };
      if (url.isNotEmpty) {
        WebRouter.jumpToWebPage(_context, url, '', params);
      }
    });

    registerHandler('pay', (message) async {
      Map<String, String> result = await PayManager.getInstance().pay(message.data);
      String callback = message.data["callback"] ?? '';
      if (callback.isNotEmpty) {
        _executeJsMethod(callback, result);
      }
    });
  }

  void _executeJsMethod(String methodName, Map<String, dynamic> params) {
    if (_webViewController == null) return;
    _webViewController!.executeJavaScript("window.$methodName('${jsonEncode(params)}')");
  }

  /// 注册消息处理器
  void registerHandler(String method, Function(JSMessage) handler) {
    _handlers[method] = handler;
  }

  /// 移除消息处理器
  void unregisterHandler(String method) {
    _handlers.remove(method);
  }

  /// 处理来自WebView的消息
  void handleMessage(String message) {
    try {
      final Map<String, dynamic> messageMap = jsonDecode(message);
      final jsMessage = JSMessage.fromJson(messageMap);
      
      // 首先尝试特定的处理器
      if (onMessage != null) {
        onMessage!(jsMessage);
        return;
      }

      // 通用的处理器
      final handler = _handlers[jsMessage.method];
      if (handler != null) {
        handler(jsMessage);
        return;
      }
    } catch (e) {
      debugPrint('解析JavaScript消息失败: $e');
      debugPrint('原始消息: $message');
    }
  }
}